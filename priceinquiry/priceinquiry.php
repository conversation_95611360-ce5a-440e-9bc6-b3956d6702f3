<?php
/**
 * <PERSON><PERSON><PERSON> "Cena na dotaz" pro PrestaShop 8.2.0
 * Zobrazuje "Cena na dotaz" místo ceny 0 Kč a umožňuje dotaz na cenu
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class PriceInquiry extends Module
{
    public function __construct()
    {
        $this->name = 'priceinquiry';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = '<PERSON><PERSON><PERSON>';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '8.2.0',
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Cena na dotaz');
        $this->description = $this->l('Zobrazuje "Cena na dotaz" místo ceny 0 Kč a umožňuje zákazníkům poslat dotaz na cenu.');
        $this->confirmUninstall = $this->l('Opravdu chcete odinstalovat modul? Všechny dotazy budou smazány.');
    }

    public function install()
    {
        include(dirname(__FILE__).'/sql/install.php');
        
        return parent::install() &&
            $this->registerHook('displayProductAdditionalInfo') &&
            $this->registerHook('displayProductPriceBlock') &&
            $this->registerHook('displayHeader') &&
            $this->registerHook('displayFooter') &&
            Configuration::updateValue('PRICE_INQUIRY_ADMIN_EMAIL', Configuration::get('PS_SHOP_EMAIL')) &&
            Configuration::updateValue('PRICE_INQUIRY_ENABLED', 1) &&
            Configuration::updateValue('PRICE_INQUIRY_BUTTON_TEXT', $this->l('Zjistit cenu')) &&
            Configuration::updateValue('PRICE_INQUIRY_PRICE_TEXT', $this->l('Cena na dotaz')) &&
            Configuration::updateValue('PRICE_INQUIRY_SEND_ADMIN_EMAIL', 1) &&
            Configuration::updateValue('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL', 1);
    }

    public function uninstall()
    {
        $sql = 'DROP TABLE IF EXISTS `'._DB_PREFIX_.'price_inquiry`';
        Db::getInstance()->execute($sql);
        
        return Configuration::deleteByName('PRICE_INQUIRY_ADMIN_EMAIL') &&
            Configuration::deleteByName('PRICE_INQUIRY_ENABLED') &&
            Configuration::deleteByName('PRICE_INQUIRY_BUTTON_TEXT') &&
            Configuration::deleteByName('PRICE_INQUIRY_PRICE_TEXT') &&
            Configuration::deleteByName('PRICE_INQUIRY_SEND_ADMIN_EMAIL') &&
            Configuration::deleteByName('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL') &&
            parent::uninstall();
    }

    public function hookDisplayHeader()
    {
        // Přidání CSS a JS souborů
        $this->context->controller->addCSS($this->_path.'views/css/front.css');
        $this->context->controller->addJS($this->_path.'views/js/front.js');
    }

    public function hookDisplayProductPriceBlock($params)
    {
        // Detekce produktu s cenou 0
        $product = $params['product'];
        $type = isset($params['type']) ? $params['type'] : '';

        // Kontrola, zda má produkt cenu 0 - různé způsoby jak PrestaShop předává cenu
        $hasZeroPrice = false;

        if (isset($product['price_amount']) && $product['price_amount'] == 0) {
            $hasZeroPrice = true;
        } elseif (isset($product['price']) && $product['price'] == 0) {
            $hasZeroPrice = true;
        } elseif (isset($product['price_tax_exc']) && $product['price_tax_exc'] == 0) {
            $hasZeroPrice = true;
        }

        if (!$hasZeroPrice) {
            return '';
        }

        // Nahrazení textu ceny - pouze jednou
        if ($type === 'before_price') {
            $price_text = Configuration::get('PRICE_INQUIRY_PRICE_TEXT') ?: $this->l('Cena na dotaz');

            // Skryjeme původní cenu a zobrazíme náš text
            return '
            <style>
                .current-price-value { display: none !important; }
                .product-price .current-price { position: relative; }
                .price-inquiry-text:not(:first-of-type) { display: none !important; }
            </style>
            <span class="price-inquiry-text">'.$price_text.'</span>';
        }

        // Zobrazení tlačítka hned pod cenou
        if ($type === 'after_price') {
            // Získání ID produktu
            $productId = isset($product['id']) ? $product['id'] : (isset($product['id_product']) ? $product['id_product'] : 0);

            if (!$productId) {
                return '';
            }

            // Získání obrázku produktu
            $image = Image::getCover($productId);
            $productImage = '';
            if ($image) {
                $linkRewrite = isset($product['link_rewrite']) ? $product['link_rewrite'] : '';
                $productImage = $this->context->link->getImageLink($linkRewrite, $image['id_image'], 'home_default');
            }

            $this->context->smarty->assign([
                'product_id' => $productId,
                'product_name' => isset($product['name']) ? $product['name'] : '',
                'product_reference' => isset($product['reference']) ? $product['reference'] : '',
                'product_image' => $productImage,
                'is_logged' => $this->context->customer->isLogged(),
                'customer_name' => $this->context->customer->firstname.' '.$this->context->customer->lastname,
                'customer_email' => $this->context->customer->email
            ]);

            return $this->display(__FILE__, 'views/templates/front/price_inquiry_button.tpl');
        }

        return '';
    }

    public function hookDisplayProductAdditionalInfo($params)
    {
        // Tento hook už nepoužíváme - tlačítko se zobrazuje v hookDisplayProductPriceBlock
        return '';
    }

    public function getContent()
    {
        // Přidání admin CSS
        $this->context->controller->addCSS($this->_path.'views/css/admin.css');

        $output = '<div class="price-inquiry-admin">';

        // Zpracování akcí
        if (Tools::isSubmit('submit'.$this->name)) {
            $admin_email = Tools::getValue('PRICE_INQUIRY_ADMIN_EMAIL');
            $enabled = Tools::getValue('PRICE_INQUIRY_ENABLED');
            $button_text = Tools::getValue('PRICE_INQUIRY_BUTTON_TEXT');
            $price_text = Tools::getValue('PRICE_INQUIRY_PRICE_TEXT');
            $send_admin_email = Tools::getValue('PRICE_INQUIRY_SEND_ADMIN_EMAIL');
            $send_customer_email = Tools::getValue('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL');

            Configuration::updateValue('PRICE_INQUIRY_ADMIN_EMAIL', $admin_email);
            Configuration::updateValue('PRICE_INQUIRY_ENABLED', $enabled);
            Configuration::updateValue('PRICE_INQUIRY_BUTTON_TEXT', $button_text);
            Configuration::updateValue('PRICE_INQUIRY_PRICE_TEXT', $price_text);
            Configuration::updateValue('PRICE_INQUIRY_SEND_ADMIN_EMAIL', $send_admin_email);
            Configuration::updateValue('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL', $send_customer_email);

            $output .= $this->displayConfirmation($this->l('Nastavení bylo uloženo.'));
        }

        // Označení dotazu jako vyřízený
        if (Tools::isSubmit('mark_resolved')) {
            $inquiry_id = (int)Tools::getValue('inquiry_id');
            if ($this->markInquiryResolved($inquiry_id)) {
                $output .= $this->displayConfirmation($this->l('Dotaz byl označen jako vyřízený.'));
            } else {
                $output .= $this->displayError($this->l('Chyba při označování dotazu.'));
            }
        }

        // Smazání dotazu
        if (Tools::isSubmit('delete_inquiry')) {
            $inquiry_id = (int)Tools::getValue('inquiry_id');
            if ($this->deleteInquiry($inquiry_id)) {
                $output .= $this->displayConfirmation($this->l('Dotaz byl smazán.'));
            } else {
                $output .= $this->displayError($this->l('Chyba při mazání dotazu.'));
            }
        }

        // Zobrazení statistik, seznamu dotazů a konfigurace
        $output .= $this->displayStatistics();
        $output .= $this->displayInquiriesList();
        $output .= '<div class="price-inquiry-config">';
        $output .= $this->displayForm();
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    public function displayForm()
    {
        // Rozšířený formulář pro konfiguraci
        $fields_form = [
            'form' => [
                'legend' => [
                    'title' => $this->l('Nastavení modulu'),
                    'icon' => 'icon-cogs'
                ],
                'input' => [
                    [
                        'type' => 'switch',
                        'label' => $this->l('Povolit modul'),
                        'name' => 'PRICE_INQUIRY_ENABLED',
                        'desc' => $this->l('Zapne/vypne funkcionalitu modulu na celém webu'),
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => 1,
                                'label' => $this->l('Ano')
                            ],
                            [
                                'id' => 'active_off',
                                'value' => 0,
                                'label' => $this->l('Ne')
                            ]
                        ]
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('E-mail administrátora'),
                        'name' => 'PRICE_INQUIRY_ADMIN_EMAIL',
                        'desc' => $this->l('E-mail, na který budou zasílány notifikace o nových dotazech'),
                        'required' => true
                    ],
                    [
                        'type' => 'textarea',
                        'label' => $this->l('Text tlačítka'),
                        'name' => 'PRICE_INQUIRY_BUTTON_TEXT',
                        'desc' => $this->l('Text zobrazený na tlačítku pro dotaz na cenu'),
                        'cols' => 40,
                        'rows' => 2
                    ],
                    [
                        'type' => 'textarea',
                        'label' => $this->l('Text místo ceny'),
                        'name' => 'PRICE_INQUIRY_PRICE_TEXT',
                        'desc' => $this->l('Text zobrazený místo ceny 0 Kč'),
                        'cols' => 40,
                        'rows' => 2
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Odesílat e-mail administrátorovi'),
                        'name' => 'PRICE_INQUIRY_SEND_ADMIN_EMAIL',
                        'desc' => $this->l('Při novém dotazu bude odeslán e-mail administrátorovi'),
                        'values' => [
                            [
                                'id' => 'admin_email_on',
                                'value' => 1,
                                'label' => $this->l('Ano')
                            ],
                            [
                                'id' => 'admin_email_off',
                                'value' => 0,
                                'label' => $this->l('Ne')
                            ]
                        ]
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Odesílat potvrzovací e-mail zákazníkovi'),
                        'name' => 'PRICE_INQUIRY_SEND_CUSTOMER_EMAIL',
                        'desc' => $this->l('Zákazník obdrží potvrzení o přijetí dotazu'),
                        'values' => [
                            [
                                'id' => 'customer_email_on',
                                'value' => 1,
                                'label' => $this->l('Ano')
                            ],
                            [
                                'id' => 'customer_email_off',
                                'value' => 0,
                                'label' => $this->l('Ne')
                            ]
                        ]
                    ]
                ],
                'submit' => [
                    'title' => $this->l('Uložit nastavení'),
                ]
            ]
        ];

        $helper = new HelperForm();
        $helper->module = $this;
        $helper->name_controller = $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex.'&configure='.$this->name;
        $helper->submit_action = 'submit'.$this->name;
        $helper->default_form_language = $this->context->language->id;

        $helper->fields_value['PRICE_INQUIRY_ENABLED'] = Configuration::get('PRICE_INQUIRY_ENABLED');
        $helper->fields_value['PRICE_INQUIRY_ADMIN_EMAIL'] = Configuration::get('PRICE_INQUIRY_ADMIN_EMAIL');
        $helper->fields_value['PRICE_INQUIRY_BUTTON_TEXT'] = Configuration::get('PRICE_INQUIRY_BUTTON_TEXT') ?: $this->l('Zjistit cenu');
        $helper->fields_value['PRICE_INQUIRY_PRICE_TEXT'] = Configuration::get('PRICE_INQUIRY_PRICE_TEXT') ?: $this->l('Cena na dotaz');
        $helper->fields_value['PRICE_INQUIRY_SEND_ADMIN_EMAIL'] = Configuration::get('PRICE_INQUIRY_SEND_ADMIN_EMAIL');
        $helper->fields_value['PRICE_INQUIRY_SEND_CUSTOMER_EMAIL'] = Configuration::get('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL');

        return $helper->generateForm([$fields_form]);
    }

    /**
     * Zobrazení statistik v admin rozhraní
     */
    public function displayStatistics()
    {
        $totalInquiries = $this->getInquiriesCount();
        $pendingInquiries = $this->getInquiriesCount(false);
        $resolvedInquiries = $this->getInquiriesCount(true);
        $todayInquiries = $this->getTodayInquiriesCount();

        $output = '<div class="price-inquiry-stats">';
        $output .= '<div class="row">';

        $output .= '<div class="col-md-3">';
        $output .= '<div class="stat-box">';
        $output .= '<span class="stat-number">'.$totalInquiries.'</span>';
        $output .= '<div class="stat-label">'.$this->l('Celkem dotazů').'</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-3">';
        $output .= '<div class="stat-box">';
        $output .= '<span class="stat-number">'.$pendingInquiries.'</span>';
        $output .= '<div class="stat-label">'.$this->l('Nevyřízené').'</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-3">';
        $output .= '<div class="stat-box">';
        $output .= '<span class="stat-number">'.$resolvedInquiries.'</span>';
        $output .= '<div class="stat-label">'.$this->l('Vyřízené').'</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '<div class="col-md-3">';
        $output .= '<div class="stat-box">';
        $output .= '<span class="stat-number">'.$todayInquiries.'</span>';
        $output .= '<div class="stat-label">'.$this->l('Dnes').'</div>';
        $output .= '</div>';
        $output .= '</div>';

        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    /**
     * Zobrazení seznamu dotazů v admin rozhraní
     */
    public function displayInquiriesList()
    {
        $inquiries = $this->getInquiries();

        $output = '<div class="panel">';
        $output .= '<div class="panel-heading">';
        $output .= '<i class="icon-list"></i> '.$this->l('Seznam dotazů na cenu');
        $output .= '</div>';

        if (empty($inquiries)) {
            $output .= '<div class="alert alert-info">'.$this->l('Zatím nebyly přijaty žádné dotazy.').'</div>';
        } else {
            $output .= '<div class="table-responsive">';
            $output .= '<table class="table table-striped">';
            $output .= '<thead>';
            $output .= '<tr>';
            $output .= '<th>'.$this->l('Datum').'</th>';
            $output .= '<th>'.$this->l('Zákazník').'</th>';
            $output .= '<th>'.$this->l('E-mail').'</th>';
            $output .= '<th>'.$this->l('Produkt').'</th>';
            $output .= '<th>'.$this->l('Zpráva').'</th>';
            $output .= '<th>'.$this->l('Stav').'</th>';
            $output .= '<th>'.$this->l('Akce').'</th>';
            $output .= '</tr>';
            $output .= '</thead>';
            $output .= '<tbody>';

            foreach ($inquiries as $inquiry) {
                $output .= '<tr>';
                $output .= '<td>'.date('d.m.Y H:i', strtotime($inquiry['date_add'])).'</td>';
                $output .= '<td>'.htmlspecialchars($inquiry['customer_name']).'</td>';
                $output .= '<td><a href="mailto:'.htmlspecialchars($inquiry['customer_email']).'">'.htmlspecialchars($inquiry['customer_email']).'</a></td>';
                $output .= '<td>';
                $output .= '<strong>'.htmlspecialchars($inquiry['product_name']).'</strong><br>';
                if ($inquiry['product_reference']) {
                    $output .= '<small>Ref: '.htmlspecialchars($inquiry['product_reference']).'</small>';
                }
                $output .= '</td>';
                $output .= '<td>'.htmlspecialchars(substr($inquiry['message'], 0, 100)).(strlen($inquiry['message']) > 100 ? '...' : '').'</td>';
                $output .= '<td>';
                if ($inquiry['resolved']) {
                    $output .= '<span class="badge badge-success">'.$this->l('Vyřízeno').'</span>';
                } else {
                    $output .= '<span class="badge badge-warning">'.$this->l('Nevyřízeno').'</span>';
                }
                $output .= '</td>';
                $output .= '<td>';
                if (!$inquiry['resolved']) {
                    $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&mark_resolved=1&inquiry_id='.$inquiry['id_inquiry'].'" class="btn btn-success btn-xs" onclick="return confirm(\''.$this->l('Označit dotaz jako vyřízený?').'\')">';
                    $output .= '<i class="icon-check"></i> '.$this->l('Vyřídit');
                    $output .= '</a> ';
                }
                $output .= '<a href="'.$this->context->link->getAdminLink('AdminModules').'&configure='.$this->name.'&delete_inquiry=1&inquiry_id='.$inquiry['id_inquiry'].'" class="btn btn-danger btn-xs" onclick="return confirm(\''.$this->l('Opravdu smazat tento dotaz?').'\')">';
                $output .= '<i class="icon-trash"></i> '.$this->l('Smazat');
                $output .= '</a>';
                $output .= '</td>';
                $output .= '</tr>';
            }

            $output .= '</tbody>';
            $output .= '</table>';
            $output .= '</div>';
        }

        $output .= '</div>';

        return $output;
    }

    /**
     * Získání všech dotazů z databáze
     */
    public function getInquiries($resolved = null)
    {
        $sql = 'SELECT * FROM `'._DB_PREFIX_.'price_inquiry`';

        if ($resolved !== null) {
            $sql .= ' WHERE resolved = '.(int)$resolved;
        }

        $sql .= ' ORDER BY date_add DESC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Označení dotazu jako vyřízený
     */
    public function markInquiryResolved($inquiry_id)
    {
        $sql = 'UPDATE `'._DB_PREFIX_.'price_inquiry`
                SET resolved = 1, date_resolved = NOW()
                WHERE id_inquiry = '.(int)$inquiry_id;

        return Db::getInstance()->execute($sql);
    }

    /**
     * Smazání dotazu
     */
    public function deleteInquiry($inquiry_id)
    {
        $sql = 'DELETE FROM `'._DB_PREFIX_.'price_inquiry`
                WHERE id_inquiry = '.(int)$inquiry_id;

        return Db::getInstance()->execute($sql);
    }

    /**
     * Získání počtu dotazů
     */
    public function getInquiriesCount($resolved = null)
    {
        $sql = 'SELECT COUNT(*) FROM `'._DB_PREFIX_.'price_inquiry`';

        if ($resolved !== null) {
            $sql .= ' WHERE resolved = '.(int)$resolved;
        }

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Získání počtu dnešních dotazů
     */
    public function getTodayInquiriesCount()
    {
        $sql = 'SELECT COUNT(*) FROM `'._DB_PREFIX_.'price_inquiry`
                WHERE DATE(date_add) = CURDATE()';

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Hook pro footer - načtení popup modalu
     */
    public function hookDisplayFooter($params)
    {
        // Načtení pouze pokud je modul aktivní
        if (!Configuration::get('PRICE_INQUIRY_ENABLED')) {
            return '';
        }

        // Přiřazení proměnných pro template
        $this->context->smarty->assign([
            'customer' => [
                'logged' => $this->context->customer->isLogged(),
                'firstname' => $this->context->customer->firstname,
                'lastname' => $this->context->customer->lastname,
                'email' => $this->context->customer->email
            ],
            'link' => $this->context->link
        ]);

        return $this->display(__FILE__, 'views/templates/front/inquiry_modal.tpl');
    }
}